#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from nav_msgs.msg import Odometry
from tf2_msgs.msg import TFMessage
import tf2_ros
import time
import threading
import subprocess
import re
from collections import defaultdict

class TransformMonitor(Node):
    def __init__(self):
        super().__init__('transform_monitor')

        # TF buffer and listener
        self.tf_buffer = tf2_ros.Buffer()
        self.tf_listener = tf2_ros.TransformListener(self.tf_buffer, self)

        # Subscribers
        self.odom_sub = self.create_subscription(
            Odometry, '/odom', self.odom_callback, 10)
        self.indoor_odom_sub = self.create_subscription(
            Odometry, '/odometry/indoor', self.indoor_odom_callback, 10)
        self.tf_sub = self.create_subscription(
            TFMessage, '/tf', self.tf_callback, 10)

        # Data storage
        self.latest_odom = None
        self.latest_indoor_odom = None
        self.tf_publishers = defaultdict(int)  # 统计每个发布者的消息数量
        self.tf_frames = set()  # 记录所有tf帧
        self.last_tf_time = time.time()
        self.tf_message_count = 0

        # Start monitoring thread
        self.monitor_thread = threading.Thread(target=self.monitor_transforms)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()

        self.get_logger().info("Transform monitor started")

    def odom_callback(self, msg):
        self.latest_odom = msg

    def indoor_odom_callback(self, msg):
        self.latest_indoor_odom = msg

    def tf_callback(self, msg):
        """处理tf消息，统计发布者信息"""
        self.tf_message_count += 1
        self.last_tf_time = time.time()

        # 记录所有的tf帧
        for transform in msg.transforms:
            self.tf_frames.add(transform.header.frame_id)
            self.tf_frames.add(transform.child_frame_id)

    def get_transform(self, target_frame, source_frame):
        try:
            transform = self.tf_buffer.lookup_transform(
                target_frame, source_frame, rclpy.time.Time())
            return transform
        except Exception as e:
            return None

    def get_tf_topic_info(self):
        """获取/tf话题的详细信息"""
        try:
            # 执行 ros2 topic info /tf --verbose 命令
            result = subprocess.run(
                ['ros2', 'topic', 'info', '/tf', '--verbose'],
                capture_output=True, text=True, timeout=5
            )

            if result.returncode == 0:
                return self.parse_topic_info(result.stdout)
            else:
                return {"error": f"命令执行失败: {result.stderr}"}
        except subprocess.TimeoutExpired:
            return {"error": "命令执行超时"}
        except Exception as e:
            return {"error": f"执行命令时出错: {str(e)}"}

    def parse_topic_info(self, output):
        """解析topic info输出"""
        info = {
            "publishers": [],
            "subscribers": [],
            "publisher_count": 0,
            "subscriber_count": 0
        }

        lines = output.split('\n')
        pending_node_name = None

        for line in lines:
            line = line.strip()
            if "Publisher count:" in line:
                match = re.search(r'(\d+)', line)
                if match:
                    info["publisher_count"] = int(match.group(1))
            elif "Subscription count:" in line:
                match = re.search(r'(\d+)', line)
                if match:
                    info["subscriber_count"] = int(match.group(1))
            elif line.startswith("Node name:"):
                pending_node_name = line.split(":", 1)[1].strip()
            elif line.startswith("Endpoint type: PUBLISHER"):
                if pending_node_name and pending_node_name not in info["publishers"]:
                    info["publishers"].append(pending_node_name)
                pending_node_name = None
            elif line.startswith("Endpoint type: SUBSCRIPTION"):
                if pending_node_name and pending_node_name not in info["subscribers"]:
                    info["subscribers"].append(pending_node_name)
                pending_node_name = None

        return info

    def get_node_status(self, node_name):
        """获取节点状态信息"""
        try:
            # 定义已知的lifecycle wrapper映射
            lifecycle_wrappers = {
                'ekf_filter_node_odom': '/robot_localization_lifecycle_wrapper',
                'ekf_filter_node_map': '/robot_localization_lifecycle_wrapper',
                'ekf_filter_node_indoor': '/indoor_ekf_lifecycle_wrapper',
                'amcl': '/amcl'
            }

            # 首先尝试直接检查节点
            result = subprocess.run(
                ['ros2', 'node', 'info', node_name],
                capture_output=True, text=True, timeout=3
            )

            if result.returncode == 0:
                return "运行中"

            # 如果直接检查失败，尝试检查lifecycle状态
            lifecycle_result = subprocess.run(
                ['ros2', 'lifecycle', 'get', node_name],
                capture_output=True, text=True, timeout=3
            )

            if lifecycle_result.returncode == 0:
                status_output = lifecycle_result.stdout.strip()
                if "active" in status_output:
                    return "运行中(lifecycle)"
                elif "inactive" in status_output:
                    return "非活跃(lifecycle)"
                elif "unconfigured" in status_output:
                    return "未配置(lifecycle)"
                else:
                    return f"lifecycle状态: {status_output}"

            # 检查是否有对应的lifecycle wrapper
            if node_name in lifecycle_wrappers:
                wrapper_name = lifecycle_wrappers[node_name]
                wrapper_result = subprocess.run(
                    ['ros2', 'lifecycle', 'get', wrapper_name],
                    capture_output=True, text=True, timeout=3
                )

                if wrapper_result.returncode == 0:
                    status_output = wrapper_result.stdout.strip()
                    if "active" in status_output:
                        return "运行中(wrapper)"
                    elif "inactive" in status_output:
                        return "非活跃(wrapper)"
                    else:
                        return f"wrapper状态: {status_output}"

            return "不可用"
        except:
            return "未知"

    def monitor_transforms(self):
        while rclpy.ok():
            print("\n" + "="*80)
            print(f"时间: {time.strftime('%H:%M:%S')}")
            print("="*80)

            # 获取TF变换
            map_to_odom = self.get_transform('map', 'odom')
            odom_to_baselink = self.get_transform('odom', 'base_link')

            if map_to_odom:
                print(f"map->odom: x={map_to_odom.transform.translation.x:.3f}, "
                      f"y={map_to_odom.transform.translation.y:.3f}, "
                      f"yaw={map_to_odom.transform.rotation.z:.3f}")
            else:
                print("map->odom: 变换不可用")

            if odom_to_baselink:
                print(f"odom->base_link: x={odom_to_baselink.transform.translation.x:.3f}, "
                      f"y={odom_to_baselink.transform.translation.y:.3f}, "
                      f"yaw={odom_to_baselink.transform.rotation.z:.3f}")
            else:
                print("odom->base_link: 变换不可用")

            # 显示odom话题数据
            if self.latest_odom:
                print(f"原始odom: x={self.latest_odom.pose.pose.position.x:.3f}, "
                      f"y={self.latest_odom.pose.pose.position.y:.3f}, "
                      f"yaw={self.latest_odom.pose.pose.orientation.z:.3f}")
                print(f"原始odom速度: vx={self.latest_odom.twist.twist.linear.x:.3f}, "
                      f"vy={self.latest_odom.twist.twist.linear.y:.3f}, "
                      f"vz={self.latest_odom.twist.twist.angular.z:.3f}")
            else:
                print("原始odom: 数据不可用")

            # 显示室内EKF输出
            if self.latest_indoor_odom:
                print(f"室内EKF: x={self.latest_indoor_odom.pose.pose.position.x:.3f}, "
                      f"y={self.latest_indoor_odom.pose.pose.position.y:.3f}, "
                      f"yaw={self.latest_indoor_odom.pose.pose.orientation.z:.3f}")
                print(f"室内EKF速度: vx={self.latest_indoor_odom.twist.twist.linear.x:.3f}, "
                      f"vy={self.latest_indoor_odom.twist.twist.linear.y:.3f}, "
                      f"vz={self.latest_indoor_odom.twist.twist.angular.z:.3f}")
            else:
                print("室内EKF: 数据不可用")

            # 显示TF话题信息
            print("\n--- TF话题监控信息 ---")
            tf_info = self.get_tf_topic_info()

            if "error" in tf_info:
                print(f"获取TF话题信息失败: {tf_info['error']}")
            else:
                print(f"TF发布者数量: {tf_info['publisher_count']}")
                print(f"TF订阅者数量: {tf_info['subscriber_count']}")

                if tf_info['publishers']:
                    print("TF发布者节点:")
                    for i, publisher in enumerate(tf_info['publishers'], 1):
                        status = self.get_node_status(publisher)
                        print(f"  {i}. {publisher} (状态: {status})")
                else:
                    print("未找到TF发布者")

            # 显示TF统计信息
            current_time = time.time()
            time_since_last_tf = current_time - self.last_tf_time
            print(f"\nTF消息统计:")
            print(f"  总消息数: {self.tf_message_count}")
            print(f"  距离上次TF消息: {time_since_last_tf:.1f}秒")
            print(f"  检测到的TF帧数量: {len(self.tf_frames)}")

            if self.tf_frames:
                print("  TF帧列表:", ", ".join(sorted(self.tf_frames)))

            time.sleep(2)  # 每2秒更新一次

def main():
    rclpy.init()
    monitor = TransformMonitor()

    try:
        rclpy.spin(monitor)
    except KeyboardInterrupt:
        print("\n监控停止")
    finally:
        monitor.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
